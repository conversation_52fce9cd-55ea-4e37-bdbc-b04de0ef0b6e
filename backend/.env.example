# Doc2Dev Configuration Example
# Copy this file to .env and configure your settings

# =============================================================================
# GitHub OAuth Configuration
# =============================================================================
# GitHub OAuth App credentials (create at https://github.com/settings/applications/new)
GITHUB_CLIENT_ID=
GITHUB_CLIENT_SECRET=

# JWT Secret Key for authentication tokens (change in production)
# You can generate a random secret key using the following command: openssl rand -base64 32
JWT_SECRET_KEY=

# =============================================================================
# CORS Configuration
# =============================================================================
# Comma-separated list of allowed origins for CORS
# For Railway deployment, add your frontend domain here
# Example: https://your-frontend.railway.app,https://your-custom-domain.com
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# =============================================================================
# Metadata Database Configuration
# =============================================================================
# Specify database type: mysql or postgresql
METADATA_DB_CONFIG_TYPE=mysql

# MySQL Configuration (when METADATA_DB_CONFIG_TYPE=mysql)
METADATA_DB_MYSQL_HOST=your_mysql_host
METADATA_DB_MYSQL_PORT=3306
METADATA_DB_MYSQL_USER=your_mysql_user
METADATA_DB_MYSQL_PASSWORD=your_mysql_password
METADATA_DB_MYSQL_DATABASE=doc2dev

# PostgreSQL Configuration (when METADATA_DB_CONFIG_TYPE=postgresql)
# METADATA_DB_POSTGRESQL_HOST=your_postgresql_host
# METADATA_DB_POSTGRESQL_PORT=5432
# METADATA_DB_POSTGRESQL_USER=your_postgresql_user
# METADATA_DB_POSTGRESQL_PASSWORD=your_postgresql_password
# METADATA_DB_POSTGRESQL_DATABASE=doc2dev

# =============================================================================
# Vector Store Configuration
# =============================================================================
# Specify vector store type: oceanbase, qdrant, chroma, pgvector, elasticsearch
VECTOR_STORE_CONFIG_TYPE=oceanbase

# OceanBase Vector Store Configuration (when VECTOR_STORE_CONFIG_TYPE=oceanbase)
VECTOR_STORE_OCEANBASE_HOST=
VECTOR_STORE_OCEANBASE_PORT=
VECTOR_STORE_OCEANBASE_USER=
VECTOR_STORE_OCEANBASE_PASSWORD=
VECTOR_STORE_OCEANBASE_DB_NAME=

# Qdrant Vector Store Configuration (when VECTOR_STORE_CONFIG_TYPE=qdrant)
# VECTOR_STORE_QDRANT_URL=https://your-qdrant-instance.qdrant.io:6333
# VECTOR_STORE_QDRANT_API_KEY=your_qdrant_api_key
# VECTOR_STORE_QDRANT_DISTANCE_STRATEGY=COSINE

# Chroma Vector Store Configuration (when VECTOR_STORE_CONFIG_TYPE=chroma)
# VECTOR_STORE_CHROMA_PERSIST_DIRECTORY=./chroma_db

# PGVector Configuration (when VECTOR_STORE_CONFIG_TYPE=pgvector)
# VECTOR_STORE_PGVECTOR_HOST=localhost
# VECTOR_STORE_PGVECTOR_PORT=5432
# VECTOR_STORE_PGVECTOR_DATABASE=postgres
# VECTOR_STORE_PGVECTOR_USER=postgres
# VECTOR_STORE_PGVECTOR_PASSWORD=postgres
# VECTOR_STORE_PGVECTOR_SCHEMA_NAME=public
# VECTOR_STORE_PGVECTOR_DISTANCE_STRATEGY=COSINE

# Elasticsearch Configuration (when VECTOR_STORE_CONFIG_TYPE=elasticsearch)
# VECTOR_STORE_ELASTICSEARCH_URL=http://localhost:9200
# VECTOR_STORE_ELASTICSEARCH_USERNAME=your_username
# VECTOR_STORE_ELASTICSEARCH_PASSWORD=your_password

# =============================================================================
# Embedding Configuration
# =============================================================================
# Specify embedding provider: openai, dashscope, huggingface, ollama
EMBEDDING_CONFIG_TYPE=dashscope

# DashScope Configuration (when EMBEDDING_CONFIG_TYPE=dashscope)
EMBEDDING_DASHSCOPE_API_KEY=
EMBEDDING_DASHSCOPE_MODEL=

# OpenAI Embedding Configuration (when EMBEDDING_CONFIG_TYPE=openai)
# EMBEDDING_OPENAI_API_KEY=your_openai_api_key
# EMBEDDING_OPENAI_MODEL=text-embedding-ada-002

# =============================================================================
# LLM Configuration
# =============================================================================
# Specify which LLM provider to use: openai, anthropic, huggingface, ollama, azure_openai
LLM_CONFIG_TYPE=openai

# OpenAI Configuration (when LLM_CONFIG_TYPE=openai)
LLM_OPENAI_API_KEY=
LLM_OPENAI_API_BASE=
LLM_OPENAI_MODEL=

# Anthropic Configuration (when LLM_CONFIG_TYPE=anthropic)
# LLM_ANTHROPIC_API_KEY=your_anthropic_api_key
# LLM_ANTHROPIC_MODEL=claude-3-opus-20240229

# HuggingFace Configuration (when LLM_CONFIG_TYPE=huggingface)
# LLM_HUGGINGFACE_API_KEY=your_huggingface_token
# LLM_HUGGINGFACE_MODEL=microsoft/DialoGPT-medium

# Ollama Configuration (when LLM_CONFIG_TYPE=ollama)
# LLM_OLLAMA_BASE_URL=http://localhost:11434
# LLM_OLLAMA_MODEL=llama2

# =============================================================================
# Application Configuration
# =============================================================================
# API Base URL for the application
API_BASE_URL=http://localhost:8000

# GitHub Token for downloading files from Public GitHub using Github API
GITHUB_TOKEN=
# Gitlab Token for downloading files from Public Gitlab using Gitlab API
GITLAB_TOKEN=

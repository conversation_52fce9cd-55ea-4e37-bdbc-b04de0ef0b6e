"""
Vector store factory for creating vector store instances based on configuration.
"""

from typing import Optional
from langchain.vectorstores.base import VectorStore
from langchain.embeddings.base import Embeddings

# Vector store imports
from langchain_community.vectorstores import Chroma
from langchain_community.vectorstores.pgvector import PGVector
from langchain_community.vectorstores import Qdrant
from qdrant_client import QdrantClient
from langchain_community.vectorstores import ElasticsearchStore

from config.vector_store import VectorStoreConfig


class VectorStoreFactory:
    """Factory for creating vector store instances based on configuration."""
    
    @staticmethod
    def create_vector_store(
        vector_store_config: VectorStoreConfig,
        embeddings: Embeddings,
        table_name: str,
        user_id: Optional[str] = None,
        db_router = None
    ) -> VectorStore:
        """
        Create a vector store instance based on configuration.

        Args:
            vector_store_config: Vector store configuration
            embeddings: Embedding model instance
            table_name: Table name for the vector store
            user_id: Optional user ID for user-specific database selection
            db_router: Optional database router for multi-tenant support

        Returns:
            VectorStore instance

        Raises:
            ValueError: If vector store type is not supported
        """
        store_type = vector_store_config.config.type
        
        match store_type:
            case "oceanbase":
                # Direct use of LangChain OceanBase implementation
                try:
                    from langchain_oceanbase.vectorstores import OceanbaseVectorStore

                    # Determine database name based on user context
                    db_name = vector_store_config.config.db_name  # Default to public database
                    if user_id and db_router:
                        # Use user-specific database for authenticated users
                        db_name = db_router.get_user_db_name(user_id)

                    return OceanbaseVectorStore(
                        embedding_function=embeddings,
                        table_name=table_name.replace('-', '_'),  # Ensure string type and sanitize table name
                        connection_args={
                            "host": vector_store_config.config.host,
                            "port": str(vector_store_config.config.port),
                            "user": vector_store_config.config.user,
                            "password": vector_store_config.config.password,
                            "db_name": db_name,
                        },
                    )
                except ImportError:
                    raise ImportError(
                        "OceanBase vector store requires 'langchain-oceanbase' package. "
                        "Install it with: pip install langchain-oceanbase"
                    )
            
            case "chroma":
                # Determine collection name with optional db_name prefix
                collection_name = table_name.replace('-', '_')
                if user_id and db_router:
                    # Use user-specific database for authenticated users
                    db_name = db_router.get_user_db_name(user_id)
                    collection_name = f"{db_name}_{collection_name}"

                return Chroma(
                    embedding_function=embeddings,
                    persist_directory=vector_store_config.config.persist_directory,
                    collection_name=collection_name,
                )

            case "pgvector":
                # Build connection string from individual components
                connection_string = (
                    f"postgresql+psycopg2://{vector_store_config.config.user}:{vector_store_config.config.password}"
                    f"@{vector_store_config.config.host}:{vector_store_config.config.port}/{vector_store_config.config.database}"
                )
                return PGVector(
                    embedding_function=embeddings,
                    connection_string=connection_string,
                    collection_name=table_name.replace('-', '_'),  # Use dynamic table_name
                    distance_strategy=vector_store_config.config.distance_strategy,
                )
            
            case "qdrant":
                # Create Qdrant client with configuration
                client = QdrantClient(
                    url=vector_store_config.config.url,
                    api_key=vector_store_config.config.api_key,
                )

                # Determine collection name with optional db_name prefix
                collection_name = table_name.replace('-', '_')
                if user_id and db_router:
                    # Use user-specific database for authenticated users
                    db_name = db_router.get_user_db_name(user_id)
                    collection_name = f"{db_name}_{collection_name}"

                # Create Qdrant vector store with proper metadata indexing
                from qdrant_client.models import PayloadSchemaType

                qdrant_store = Qdrant(
                    client=client,
                    collection_name=collection_name,
                    embeddings=embeddings,
                    distance_strategy=vector_store_config.config.distance_strategy,
                )

                # Ensure collection exists and has proper indexes for metadata filtering
                try:
                    # Check if collection exists
                    collections = client.get_collections()
                    collection_exists = any(col.name == collection_name for col in collections.collections)

                    if collection_exists:
                        # Create index for table_name metadata field if it doesn't exist
                        try:
                            client.create_payload_index(
                                collection_name=collection_name,
                                field_name="metadata.table_name",
                                field_schema=PayloadSchemaType.KEYWORD
                            )
                        except Exception:
                            # Index might already exist, ignore error
                            pass
                except Exception as e:
                    print(f"Warning: Could not ensure Qdrant indexes: {e}")

                return qdrant_store
            
            case "elasticsearch":
                return ElasticsearchStore(
                    es_url=vector_store_config.config.url,
                    index_name=table_name.replace('-', '_'),  # Use dynamic table_name
                    embedding=embeddings,
                    es_user=vector_store_config.config.username,
                    es_password=vector_store_config.config.password,
                )
            
            case _:
                raise ValueError(f"Unsupported vector store type: {store_type}")

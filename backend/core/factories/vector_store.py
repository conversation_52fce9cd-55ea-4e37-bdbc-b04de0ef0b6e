"""
Vector store factory for creating vector store instances based on configuration.
"""

from typing import Optional
from langchain.vectorstores.base import VectorStore
from langchain.embeddings.base import Embeddings

# Vector store imports
from langchain_community.vectorstores import Chroma
from langchain_community.vectorstores.pgvector import PGVector
from langchain_qdrant import QdrantVectorStore
from qdrant_client import QdrantClient
from qdrant_client.http.models import Distance, VectorParams
from langchain_community.vectorstores import ElasticsearchStore

from config.vector_store import VectorStoreConfig


class VectorStoreFactory:
    """Factory for creating vector store instances based on configuration."""
    
    @staticmethod
    def create_vector_store(
        vector_store_config: VectorStoreConfig,
        embeddings: Embeddings,
        table_name: str,
        user_id: Optional[str] = None,
        db_router = None
    ) -> VectorStore:
        """
        Create a vector store instance based on configuration.

        Args:
            vector_store_config: Vector store configuration
            embeddings: Embedding model instance
            table_name: Table name for the vector store
            user_id: Optional user ID for user-specific database selection
            db_router: Optional database router for multi-tenant support

        Returns:
            VectorStore instance

        Raises:
            ValueError: If vector store type is not supported
        """
        store_type = vector_store_config.config.type
        
        match store_type:
            case "oceanbase":
                # Direct use of LangChain OceanBase implementation
                try:
                    from langchain_oceanbase.vectorstores import OceanbaseVectorStore

                    # Determine database name based on user context
                    db_name = vector_store_config.config.db_name  # Default to public database
                    if user_id and db_router:
                        # Use user-specific database for authenticated users
                        db_name = db_router.get_user_db_name(user_id)

                    return OceanbaseVectorStore(
                        embedding_function=embeddings,
                        table_name=table_name.replace('-', '_'),  # Ensure string type and sanitize table name
                        connection_args={
                            "host": vector_store_config.config.host,
                            "port": str(vector_store_config.config.port),
                            "user": vector_store_config.config.user,
                            "password": vector_store_config.config.password,
                            "db_name": db_name,
                        },
                    )
                except ImportError:
                    raise ImportError(
                        "OceanBase vector store requires 'langchain-oceanbase' package. "
                        "Install it with: pip install langchain-oceanbase"
                    )
            
            case "chroma":
                # Determine collection name with optional db_name prefix
                collection_name = table_name.replace('-', '_')
                if user_id and db_router:
                    # Use user-specific database for authenticated users
                    db_name = db_router.get_user_db_name(user_id)
                    collection_name = f"{db_name}_{collection_name}"

                return Chroma(
                    embedding_function=embeddings,
                    persist_directory=vector_store_config.config.persist_directory,
                    collection_name=collection_name,
                )

            case "pgvector":
                # Build connection string from individual components
                connection_string = (
                    f"postgresql+psycopg2://{vector_store_config.config.user}:{vector_store_config.config.password}"
                    f"@{vector_store_config.config.host}:{vector_store_config.config.port}/{vector_store_config.config.database}"
                )
                return PGVector(
                    embedding_function=embeddings,
                    connection_string=connection_string,
                    collection_name=table_name.replace('-', '_'),  # Use dynamic table_name
                    distance_strategy=vector_store_config.config.distance_strategy,
                )
            
            case "qdrant":
                # Create Qdrant client with configuration
                client = QdrantClient(
                    url=vector_store_config.config.url,
                    api_key=vector_store_config.config.api_key,
                )

                # Determine collection name with optional db_name prefix
                collection_name = table_name.replace('-', '_')
                if user_id and db_router:
                    # Use user-specific database for authenticated users
                    db_name = db_router.get_user_db_name(user_id)
                    collection_name = f"{db_name}_{collection_name}"


                try:
                    client.create_collection(
                        collection_name=collection_name,
                        vectors_config=VectorParams(size=1024, distance=Distance.COSINE),
                    )
                except Exception as e:
                    print(f"Collection {collection_name} already exists: {e}")

                return QdrantVectorStore(
                    client=client,
                    collection_name=collection_name,
                    embedding=embeddings,
                    distance=vector_store_config.config.distance_strategy,
                )
            
            case "elasticsearch":
                return ElasticsearchStore(
                    es_url=vector_store_config.config.url,
                    index_name=table_name.replace('-', '_'),  # Use dynamic table_name
                    embedding=embeddings,
                    es_user=vector_store_config.config.username,
                    es_password=vector_store_config.config.password,
                )
            
            case _:
                raise ValueError(f"Unsupported vector store type: {store_type}")
